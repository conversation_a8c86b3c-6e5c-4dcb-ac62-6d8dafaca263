// TuanziApp/src/components/EditReservationModal.tsx

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { Reservation } from '../types';
import { theme } from '../styles/theme';
import { updateReservation, formatScheduleTime, validateScheduleTime } from '../api/calendarApi';

interface EditReservationModalProps {
  visible: boolean;
  onClose: () => void;
  reservation: Reservation | null;
  onReservationUpdated?: () => void;
}

export const EditReservationModal: React.FC<EditReservationModalProps> = ({
  visible,
  onClose,
  reservation,
  onReservationUpdated,
}) => {
  const [roomName, setRoomName] = useState('');
  const [scheduledDateTime, setScheduledDateTime] = useState(new Date());
  const [durationHours, setDurationHours] = useState(2);
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (visible && reservation) {
      setRoomName(reservation.name);
      setScheduledDateTime(new Date(reservation.start_time));
      setDurationHours(reservation.duration_hours);
    }
  }, [visible, reservation]);

  // 处理日期时间变化
  const onDateTimeChange = (_: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
      setShowTimePicker(false);
    }

    if (selectedDate) {
      setScheduledDateTime(selectedDate);
    }
  };

  // 验证表单
  const validateForm = (): string | null => {
    if (!roomName.trim()) {
      return '请输入房间名称';
    }

    if (!validateScheduleTime(scheduledDateTime)) {
      return '预约时间必须是未来时间';
    }

    // 检查是否修改时间过于接近当前时间
    const now = new Date();
    const timeDifference = scheduledDateTime.getTime() - now.getTime();
    const minutesUntilStart = Math.floor(timeDifference / (1000 * 60));

    if (minutesUntilStart < 60) {
      return '预约时间必须至少在1小时后';
    }

    if (durationHours < 1 || durationHours > 72) {
      return '持续时长必须在1-72小时之间';
    }

    return null;
  };

  // 提交修改
  const handleSubmit = async () => {
    if (!reservation || loading) {
      return;
    }

    const validationError = validateForm();
    if (validationError) {
      Alert.alert('验证失败', validationError);
      return;
    }

    try {
      setLoading(true);

      const updateData = {
        name: roomName.trim(),
        scheduled_start_time: formatScheduleTime(scheduledDateTime),
        duration_hours: durationHours,
      };

      await updateReservation(reservation.id, updateData);
      
      Alert.alert('修改成功', '预约信息已更新');
      onReservationUpdated?.();
      onClose();
    } catch (error) {
      console.error('Failed to update reservation:', error);
      Alert.alert('修改失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 检查是否有修改
  const hasChanges = () => {
    if (!reservation) return false;
    
    const originalDateTime = new Date(reservation.start_time);
    return (
      roomName.trim() !== reservation.name ||
      scheduledDateTime.getTime() !== originalDateTime.getTime() ||
      durationHours !== reservation.duration_hours
    );
  };

  if (!reservation) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        {/* 模态框头部 */}
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>修改预约</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* 内容区域 */}
        <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
          {/* 房间名称 */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>房间名称</Text>
            <TextInput
              style={styles.textInput}
              value={roomName}
              onChangeText={setRoomName}
              placeholder="请输入房间名称"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          {/* 预约时间 */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>预约时间</Text>
            
            <TouchableOpacity
              style={styles.dateTimeButton}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={styles.dateTimeText}>
                日期: {scheduledDateTime.toLocaleDateString()}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.dateTimeButton}
              onPress={() => setShowTimePicker(true)}
            >
              <Text style={styles.dateTimeText}>
                时间: {scheduledDateTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </Text>
            </TouchableOpacity>

            {showDatePicker && (
              <DateTimePicker
                value={scheduledDateTime}
                mode="date"
                display="default"
                onChange={onDateTimeChange}
                minimumDate={new Date()}
              />
            )}

            {showTimePicker && (
              <DateTimePicker
                value={scheduledDateTime}
                mode="time"
                display="default"
                onChange={onDateTimeChange}
              />
            )}
          </View>

          {/* 持续时长 */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>持续时长</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={durationHours}
                onValueChange={(itemValue) => setDurationHours(itemValue)}
                style={styles.picker}
              >
                {[1, 2, 3, 4, 6, 8, 12, 24, 48, 72].map((hours) => (
                  <Picker.Item
                    key={hours}
                    label={`${hours} 小时`}
                    value={hours}
                  />
                ))}
              </Picker>
            </View>
          </View>

          {/* 预览信息 */}
          <View style={styles.previewContainer}>
            <Text style={styles.previewTitle}>修改后的预约信息</Text>
            <Text style={styles.previewText}>房间名称: {roomName || '未设置'}</Text>
            <Text style={styles.previewText}>
              开始时间: {scheduledDateTime.toLocaleString()}
            </Text>
            <Text style={styles.previewText}>
              结束时间: {new Date(scheduledDateTime.getTime() + durationHours * 60 * 60 * 1000).toLocaleString()}
            </Text>
            <Text style={styles.previewText}>持续时长: {durationHours} 小时</Text>
          </View>
        </ScrollView>

        {/* 底部操作按钮 */}
        <View style={styles.modalFooter}>
          <TouchableOpacity
            style={[styles.cancelButton, { marginRight: 12 }]}
            onPress={onClose}
          >
            <Text style={styles.cancelButtonText}>取消</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.submitButton,
              (loading || !hasChanges()) && styles.submitButtonDisabled
            ]}
            onPress={handleSubmit}
            disabled={loading || !hasChanges()}
          >
            {loading ? (
              <ActivityIndicator size="small" color={theme.colors.surface} />
            ) : (
              <Text style={styles.submitButtonText}>保存修改</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.gray200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 18,
    color: theme.colors.textSecondary,
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: theme.colors.textPrimary,
    backgroundColor: theme.colors.surface,
  },
  dateTimeButton: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    padding: 12,
    backgroundColor: theme.colors.surface,
    marginBottom: 8,
  },
  dateTimeText: {
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    backgroundColor: theme.colors.surface,
  },
  picker: {
    height: 50,
    color: theme.colors.textPrimary,
  },
  previewContainer: {
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 12,
  },
  previewText: {
    fontSize: 14,
    color: theme.colors.textPrimary,
    marginBottom: 4,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: theme.colors.gray400,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.surface,
  },
  submitButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: theme.colors.gray400,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.surface,
  },
});
