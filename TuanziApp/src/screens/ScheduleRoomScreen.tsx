// TuanziApp/src/screens/ScheduleRoomScreen.tsx

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { RootStackParamList, EventTemplate, ScheduleRoomRequest } from '../types';
import { scheduleRoom, formatScheduleTime, validateScheduleTime } from '../api/calendarApi';
import { theme } from '../styles/theme';
import { navigateBackToScreen } from '../utils/navigationHelpers';
import { TemplateSelectionModal } from '../components/TemplateSelectionModal';
import { useReservationForm, useCalendarData } from '../contexts/ReservationContext';

type ScheduleRoomScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ScheduleRoom'>;
type ScheduleRoomScreenRouteProp = RouteProp<RootStackParamList, 'ScheduleRoom'>;

interface ScheduleRoomScreenProps {}

export const ScheduleRoomScreen: React.FC<ScheduleRoomScreenProps> = () => {
  const navigation = useNavigation<ScheduleRoomScreenNavigationProp>();
  const route = useRoute<ScheduleRoomScreenRouteProp>();

  // 使用Context管理表单状态
  const {
    roomName,
    selectedTemplate,
    scheduledDateTime,
    durationHours,
    setRoomName,
    setSelectedTemplate,
    setScheduledDateTime,
    setDurationHours,
    resetForm,
  } = useReservationForm();

  const { refreshCalendarData } = useCalendarData();

  // UI状态
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [showTemplateModal, setShowTemplateModal] = useState(false);



  // 初始化时从路由参数获取选中的日期
  useEffect(() => {
    // 处理选中的日期（仅在初始化时）
    if (route.params?.selectedDate) {
      const selectedDate = new Date(route.params.selectedDate);
      // 设置为当天的下午2点
      selectedDate.setHours(14, 0, 0, 0);
      setScheduledDateTime(selectedDate);
    }
  }, [route.params?.selectedDate, setScheduledDateTime]);

  // 处理从路由参数获取的模板（如果有的话）
  useEffect(() => {
    if (route.params?.selectedTemplate) {
      setSelectedTemplate(route.params.selectedTemplate);
      // 清除参数，避免重复设置
      navigation.setParams({ selectedTemplate: undefined });
    }
  }, [route.params?.selectedTemplate, navigation, setSelectedTemplate]);



  // 处理日期时间变化
  const onDateTimeChange = (_: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
      setShowTimePicker(false);
    }

    if (selectedDate) {
      setScheduledDateTime(selectedDate);
    }
  };

  // 验证表单
  const validateForm = (): string | null => {
    if (!roomName.trim()) {
      return '请输入房间名称';
    }
    
    if (!selectedTemplate) {
      return '请选择房间模板';
    }
    
    if (!validateScheduleTime(scheduledDateTime)) {
      return '预约时间必须是未来时间';
    }
    
    if (durationHours < 1 || durationHours > 72) {
      return '持续时长必须在1-72小时之间';
    }
    
    return null;
  };

  // 提交预约
  const handleSubmit = async () => {
    // 防止重复提交
    if (loading) {
      return;
    }

    const validationError = validateForm();
    if (validationError) {
      Alert.alert('验证失败', validationError);
      return;
    }

    try {
      setLoading(true);

      // 准备模板ID
      const templateId = selectedTemplate!.id;
      // 检查是否是系统模板，如果是，需要添加前缀
      const formattedTemplateId = selectedTemplate!.type === 'system'
        ? `system_${templateId}`
        : templateId;

      const scheduleData: ScheduleRoomRequest = {
        name: roomName.trim(),
        template_id: formattedTemplateId,
        scheduled_start_time: formatScheduleTime(scheduledDateTime),
        duration_hours: durationHours,
      };

      const newRoom = await scheduleRoom(scheduleData);
      
      Alert.alert(
        '预约成功',
        `房间 ${newRoom.room_code} 已成功预约！`,
        [
          {
            text: '确定',
            onPress: async () => {
              // 重置表单
              resetForm();
              // 刷新日历数据
              await refreshCalendarData();
              // 使用导航辅助函数返回到日历页面
              navigateBackToScreen(navigation, 'Calendar');
            },
          },
        ]
      );
    } catch (error) {
      console.error('Failed to schedule room:', error);
      Alert.alert('预约失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 处理模板选择按钮点击
  const handleTemplateSelection = () => {
    setShowTemplateModal(true);
  };

  // 处理模板选择
  const handleTemplateSelected = (template: EventTemplate) => {
    setSelectedTemplate(template);
  };

  // 渲染模板选择器
  const renderTemplatePicker = () => {
    return (
      <View style={styles.inputGroup}>
        <Text style={styles.label}>房间模板</Text>
        <TouchableOpacity
          style={styles.templateSelectorButton}
          onPress={handleTemplateSelection}
        >
          <View style={styles.templateSelectorContent}>
            {selectedTemplate ? (
              <View style={styles.selectedTemplateInfo}>
                <Text style={styles.selectedTemplateName}>
                  {selectedTemplate.type === 'system' ? '🏛️' : '👤'} {selectedTemplate.name}
                </Text>
                <Text style={styles.selectedTemplateDescription}>
                  {selectedTemplate.description}
                </Text>
                {selectedTemplate.steps && selectedTemplate.steps.length > 0 && (
                  <Text style={styles.selectedTemplateSteps}>
                    包含 {selectedTemplate.steps.length} 个环节
                  </Text>
                )}
              </View>
            ) : (
              <Text style={styles.templateSelectorPlaceholder}>
                点击选择房间模板
              </Text>
            )}
            <Text style={styles.templateSelectorArrow}>›</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  // 渲染日期时间选择器
  const renderDateTimePicker = () => {
    return (
      <View style={styles.inputGroup}>
        <Text style={styles.label}>预约时间</Text>
        
        <TouchableOpacity
          style={styles.dateTimeButton}
          onPress={() => setShowDatePicker(true)}
        >
          <Text style={styles.dateTimeText}>
            日期: {scheduledDateTime.toLocaleDateString()}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.dateTimeButton}
          onPress={() => setShowTimePicker(true)}
        >
          <Text style={styles.dateTimeText}>
            时间: {scheduledDateTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
        </TouchableOpacity>

        {showDatePicker && (
          <DateTimePicker
            value={scheduledDateTime}
            mode="date"
            display="default"
            onChange={onDateTimeChange}
            minimumDate={new Date()}
          />
        )}

        {showTimePicker && (
          <DateTimePicker
            value={scheduledDateTime}
            mode="time"
            display="default"
            onChange={onDateTimeChange}
          />
        )}
      </View>
    );
  };

  // 渲染持续时长选择器
  const renderDurationPicker = () => {
    const durationOptions = [1, 2, 3, 4, 6, 8, 12, 24, 48, 72];
    
    return (
      <View style={styles.inputGroup}>
        <Text style={styles.label}>持续时长</Text>
        <View style={styles.pickerContainer}>
          <Picker
            selectedValue={durationHours}
            onValueChange={(itemValue) => setDurationHours(itemValue)}
            style={styles.picker}
          >
            {durationOptions.map((hours) => (
              <Picker.Item
                key={hours}
                label={`${hours} 小时`}
                value={hours}
              />
            ))}
          </Picker>
        </View>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>预约游戏房间</Text>
        
        {/* 房间名称输入 */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>房间名称</Text>
          <TextInput
            style={styles.textInput}
            value={roomName}
            onChangeText={setRoomName}
            placeholder="请输入房间名称"
            placeholderTextColor={theme.colors.textSecondary}
          />
        </View>

        {/* 模板选择 */}
        {renderTemplatePicker()}

        {/* 日期时间选择 */}
        {renderDateTimePicker()}

        {/* 持续时长选择 */}
        {renderDurationPicker()}

        {/* 预约信息预览 */}
        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>预约信息预览</Text>
          <Text style={styles.previewText}>房间名称: {roomName || '未设置'}</Text>
          <Text style={styles.previewText}>
            房间模板: {selectedTemplate?.name || '未选择'}
          </Text>
          <Text style={styles.previewText}>
            开始时间: {scheduledDateTime.toLocaleString()}
          </Text>
          <Text style={styles.previewText}>
            结束时间: {new Date(scheduledDateTime.getTime() + durationHours * 60 * 60 * 1000).toLocaleString()}
          </Text>
          <Text style={styles.previewText}>持续时长: {durationHours} 小时</Text>
        </View>

        {/* 提交按钮 */}
        <TouchableOpacity
          style={[
            styles.submitButton,
            loading && styles.submitButtonDisabled,
            (!roomName.trim() || !selectedTemplate) && styles.submitButtonDisabled
          ]}
          onPress={handleSubmit}
          disabled={loading || !roomName.trim() || !selectedTemplate}
        >
          {loading ? (
            <View style={styles.loadingButtonContent}>
              <ActivityIndicator size="small" color={theme.colors.surface} />
              <Text style={styles.submitButtonText}>预约中...</Text>
            </View>
          ) : (
            <Text style={styles.submitButtonText}>确认预约</Text>
          )}
        </TouchableOpacity>

        {/* 模板选择模态框 */}
        <TemplateSelectionModal
          visible={showTemplateModal}
          onClose={() => setShowTemplateModal(false)}
          onSelectTemplate={handleTemplateSelected}
          selectedTemplateId={selectedTemplate?.id}
          title="选择房间模板"
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 24,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: theme.colors.textPrimary,
    backgroundColor: theme.colors.surface,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    backgroundColor: theme.colors.surface,
  },
  picker: {
    height: 50,
    color: theme.colors.textPrimary,
  },
  templateInfo: {
    marginTop: 8,
    padding: 12,
    backgroundColor: theme.colors.background,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  templateName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    flex: 1,
  },
  templateType: {
    fontSize: 12,
    color: theme.colors.primary,
    backgroundColor: theme.colors.surface,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    fontWeight: 'bold',
  },
  templateDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    lineHeight: 20,
  },
  dateTimeButton: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    padding: 12,
    backgroundColor: theme.colors.surface,
    marginBottom: 8,
  },
  dateTimeText: {
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  previewContainer: {
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 12,
  },
  previewText: {
    fontSize: 14,
    color: theme.colors.textPrimary,
    marginBottom: 4,
  },
  submitButton: {
    backgroundColor: theme.colors.primary,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 32,
  },
  submitButtonDisabled: {
    backgroundColor: theme.colors.gray400,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.surface,
  },
  loadingButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: 16,
    color: theme.colors.error,
    textAlign: 'center',
    marginBottom: 10,
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
    marginTop: 10,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  templateSelectorContainer: {
    height: 300, // 限制高度，使其可滚动
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    backgroundColor: theme.colors.surface,
  },
  stepsPreview: {
    marginTop: 12,
    padding: 12,
    backgroundColor: theme.colors.background,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  stepsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 8,
  },
  stepItem: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  templateSelectorButton: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    backgroundColor: theme.colors.surface,
    padding: 16,
  },
  templateSelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectedTemplateInfo: {
    flex: 1,
  },
  selectedTemplateName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 4,
  },
  selectedTemplateDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  selectedTemplateSteps: {
    fontSize: 12,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  templateSelectorPlaceholder: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    flex: 1,
  },
  templateSelectorArrow: {
    fontSize: 20,
    color: theme.colors.textSecondary,
    marginLeft: 8,
  },
});
